#!/system/bin/sh
#RT-devpro+ 驱动加载脚本
#适用于安卓设备

# 颜色定义
ESC_SEQ="["; RESET_SEQ="${ESC_SEQ}0m"; COLOR_SEQ="${ESC_SEQ}38;5;";
COLOR_GREEN="${COLOR_SEQ}10m";
COLOR_RED="${COLOR_SEQ}9m";

clear
echo -e "[1m"
echo -e "${COLOR_GREEN}RT-devpro+ 驱动加载工具${RESET_SEQ}"
echo -e "${COLOR_GREEN}================================${RESET_SEQ}"

# 检查是否以root权限运行
if [ "$(id -u)" != "0" ]; then
  echo -e "${COLOR_RED}错误: 请使用root权限运行此脚本${RESET_SEQ}"
  exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR=$(dirname "$0")
cd "$SCRIPT_DIR"

# 简单查找 1.ko 文件
DRIVER_FILE="1.ko"

# 检查驱动文件是否存在
if [ ! -f "$DRIVER_FILE" ]; then
    echo -e "${COLOR_RED}错误: 驱动文件 ${DRIVER_FILE} 不存在${RESET_SEQ}"
    echo -e "${COLOR_RED}请将要测试的驱动文件重命名为 1.ko${RESET_SEQ}"
    exit 1
fi

echo -e "${COLOR_GREEN}准备加载驱动: ${DRIVER_FILE}${RESET_SEQ}"

# 加载驱动
echo -e "${COLOR_GREEN}正在加载驱动...${RESET_SEQ}"
insmod "$DRIVER_FILE"

# 检查加载结果
if [ $? -eq 0 ]; then
    echo -e "${COLOR_GREEN}================================${RESET_SEQ}"
    for i in $(seq 1 30); do
        echo -e "${COLOR_GREEN}RT驱动加载成功!!!! 频道@RTdrivers${RESET_SEQ}"
    done
    echo -e "${COLOR_GREEN}================================${RESET_SEQ}"

    # 清理dmesg
    dmesg -C 2>/dev/null

else
    echo -e "${COLOR_RED}================================${RESET_SEQ}"
    echo -e "${COLOR_RED}驱动加载失败!!!!${RESET_SEQ}"
    echo -e "${COLOR_RED}================================${RESET_SEQ}"
    exit 1
fi

exit 0
