#!/usr/bin/env python3
"""
驱动提取脚本
从 .sh 文件中提取内核驱动模块并按照内核版本命名为 kernel_x_x 格式
"""

import os
import re
import glob
import sys
from pathlib import Path

def get_output_filename(filename):
    """从文件名生成输出文件名"""
    # 直接使用文件名，去掉 .sh 后缀，加上 .ko 后缀
    if filename.endswith('.sh'):
        return filename[:-3] + '.ko'
    return filename + '.ko'

def find_elf_start(content):
    """查找 ELF 文件的开始位置"""
    # ELF 文件以 0x7f 'ELF' 开头
    elf_magic = b'\x7fELF'
    return content.find(elf_magic)

def extract_driver_from_sh(sh_file_path):
    """从 .sh 文件中提取驱动"""
    try:
        with open(sh_file_path, 'rb') as f:
            content = f.read()
        
        # 查找分隔符，通常是 "#BY x Kitten x 离" 或类似的标记
        # 从文件内容来看，二进制数据在这个标记之后
        separator_patterns = [
            b'#BY x Kitten x \xe7\xa6\xbb',  # "#BY x Kitten x 离"
            b'#BY@BY520A',
            b'ELF'  # 直接查找 ELF 标记
        ]
        
        binary_start = -1
        for pattern in separator_patterns:
            pos = content.find(pattern)
            if pattern == b'ELF':
                # 查找 ELF 魔数
                elf_pos = find_elf_start(content)
                if elf_pos != -1:
                    binary_start = elf_pos
                    break
            elif pos != -1:
                # 找到分隔符后，查找下一个 ELF 开始
                remaining_content = content[pos:]
                elf_pos = find_elf_start(remaining_content)
                if elf_pos != -1:
                    binary_start = pos + elf_pos
                    break
        
        if binary_start == -1:
            print(f"警告: 在 {sh_file_path} 中未找到 ELF 数据")
            return None
        
        # 提取二进制数据
        binary_data = content[binary_start:]
        
        # 验证是否为有效的 ELF 文件
        if not binary_data.startswith(b'\x7fELF'):
            print(f"警告: {sh_file_path} 中提取的数据不是有效的 ELF 文件")
            return None
        
        return binary_data
        
    except Exception as e:
        print(f"错误: 处理文件 {sh_file_path} 时出错: {e}")
        return None

def main():
    """主函数"""
    print("RT-devpro+ 驱动提取工具")
    print("=" * 50)
    
    # 获取当前目录下所有的 .sh 文件
    sh_files = glob.glob("*.sh")
    
    if not sh_files:
        print("错误: 当前目录下没有找到 .sh 文件")
        return
    
    # 创建输出目录
    output_dir = Path("extracted_drivers")
    output_dir.mkdir(exist_ok=True)
    
    extracted_count = 0
    failed_count = 0
    
    for sh_file in sorted(sh_files):
        print(f"\n处理文件: {sh_file}")

        # 生成输出文件名
        output_filename = get_output_filename(sh_file)
        
        # 提取驱动
        driver_data = extract_driver_from_sh(sh_file)
        if driver_data is None:
            print(f"  失败: 无法从 {sh_file} 中提取驱动")
            failed_count += 1
            continue
        
        # 保存驱动文件
        output_file = output_dir / output_filename
        try:
            with open(output_file, 'wb') as f:
                f.write(driver_data)
            
            print(f"  成功: 提取驱动到 {output_file}")
            print(f"  大小: {len(driver_data)} 字节")

            # 在 Linux 系统上验证文件是否可以被 modinfo 识别
            if os.name == 'posix':  # Linux/Unix 系统
                if os.system(f"modinfo {output_file} > /dev/null 2>&1") == 0:
                    print(f"  验证: ✓ 驱动文件格式正确，可以使用 insmod")
                else:
                    print(f"  验证: ⚠ 驱动文件可能有问题，请手动检查")
            else:
                print(f"  验证: 跳过验证（非 Linux 系统）")

            extracted_count += 1
            
        except Exception as e:
            print(f"  错误: 保存文件时出错: {e}")
            failed_count += 1
    
    print("\n" + "=" * 50)
    print(f"提取完成!")
    print(f"成功提取: {extracted_count} 个驱动")
    print(f"失败: {failed_count} 个文件")
    print(f"输出目录: {output_dir.absolute()}")
    
    if extracted_count > 0:
        print(f"\n使用方法:")
        print(f"  sudo insmod {output_dir}/文件名.ko")
        print(f"  例如: sudo insmod {output_dir}/4.9.186.ko")

if __name__ == "__main__":
    main()
