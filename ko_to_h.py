#!/usr/bin/env python3
"""
KO 转 H 文件脚本
将 .ko 驱动文件转换为 .h 头文件，以便嵌入到其他程序中
"""

import os
import re
import glob
import sys
from pathlib import Path

def extract_version_from_filename(filename):
    """从文件名中提取版本信息"""
    # 去掉 .ko 后缀
    name = filename.replace('.ko', '')
    
    # 匹配版本号格式：x.x.x 或 x.x
    patterns = [
        r'^(\d+)\.(\d+)\.(\d+)[a-z]*$',  # 4.9.186, 4.14.186b 等
        r'^(\d+)\.(\d+)[a-z]*$',         # 5.10, 5.4b 等
    ]
    
    for pattern in patterns:
        match = re.match(pattern, name)
        if match:
            if len(match.groups()) == 3:
                major, minor, patch = match.groups()
                return f"kernel_{major}_{minor}_{patch}"
            else:
                major, minor = match.groups()
                return f"kernel_{major}_{minor}_0"
    
    # 如果无法匹配，使用原文件名
    return f"kernel_{name.replace('.', '_').replace('-', '_')}"

def ko_to_h_array(ko_file_path, output_h_path, array_name):
    """将 .ko 文件转换为 C 头文件数组"""
    try:
        with open(ko_file_path, 'rb') as f:
            binary_data = f.read()
        
        # 生成 C 头文件内容
        header_content = f"""#ifndef {array_name.upper()}_H
#define {array_name.upper()}_H

/*
 * 自动生成的头文件
 * 源文件: {os.path.basename(ko_file_path)}
 * 大小: {len(binary_data)} 字节
 */

static const unsigned char {array_name}_data[] = {{
"""
        
        # 将二进制数据转换为十六进制数组
        for i in range(0, len(binary_data), 16):
            chunk = binary_data[i:i+16]
            hex_values = ', '.join(f'0x{byte:02x}' for byte in chunk)
            header_content += f"    {hex_values}"
            
            # 如果不是最后一行，添加逗号
            if i + 16 < len(binary_data):
                header_content += ","
            
            header_content += "\n"
        
        header_content += f"""
}};

static const unsigned int {array_name}_size = {len(binary_data)};

#endif /* {array_name.upper()}_H */
"""
        
        # 写入头文件
        with open(output_h_path, 'w', encoding='utf-8') as f:
            f.write(header_content)
        
        return True
        
    except Exception as e:
        print(f"错误: 转换文件 {ko_file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("KO 转 H 文件工具")
    print("=" * 50)
    
    # 查找 extracted_drivers 目录
    drivers_dir = Path("extracted_drivers")
    if not drivers_dir.exists():
        print("错误: extracted_drivers 目录不存在")
        print("请先运行 extract_drivers.py 提取驱动文件")
        return
    
    # 获取所有 .ko 文件
    ko_files = list(drivers_dir.glob("*.ko"))
    
    if not ko_files:
        print("错误: extracted_drivers 目录下没有找到 .ko 文件")
        return
    
    # 创建输出目录
    output_dir = Path("kernel_headers")
    output_dir.mkdir(exist_ok=True)
    
    converted_count = 0
    failed_count = 0
    
    for ko_file in sorted(ko_files):
        print(f"\n处理文件: {ko_file.name}")
        
        # 提取版本信息生成头文件名
        header_name = extract_version_from_filename(ko_file.name)
        output_file = output_dir / f"{header_name}.h"
        
        # 生成数组名（去掉非法字符）
        array_name = header_name.lower()
        
        print(f"  输出文件: {output_file}")
        print(f"  数组名: {array_name}_data")
        
        # 转换文件
        if ko_to_h_array(ko_file, output_file, array_name):
            file_size = ko_file.stat().st_size
            print(f"  成功: 转换完成")
            print(f"  原始大小: {file_size} 字节")
            converted_count += 1
        else:
            print(f"  失败: 转换失败")
            failed_count += 1
    
    print("\n" + "=" * 50)
    print(f"转换完成!")
    print(f"成功转换: {converted_count} 个文件")
    print(f"失败: {failed_count} 个文件")
    print(f"输出目录: {output_dir.absolute()}")
    
    if converted_count > 0:
        print(f"\n使用方法:")
        print(f"  #include \"kernel_headers/{header_name}.h\"")
        print(f"  // 数据: {array_name}_data")
        print(f"  // 大小: {array_name}_size")
        print(f"\n示例代码:")
        print(f"  // 写入临时文件并加载")
        print(f"  FILE *fp = fopen(\"/tmp/driver.ko\", \"wb\");")
        print(f"  fwrite({array_name}_data, 1, {array_name}_size, fp);")
        print(f"  fclose(fp);")
        print(f"  system(\"insmod /tmp/driver.ko\");")

if __name__ == "__main__":
    main()
